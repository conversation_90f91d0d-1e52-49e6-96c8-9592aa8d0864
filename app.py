from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
import requests
import json
import fitz
import faiss
import numpy as np
import logging
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_experimental.text_splitter import SemanticChunker
from langchain_openai.embeddings import OpenAIEmbeddings
from concurrent.futures import ThreadPoolExecutor
import re
from bs4 import BeautifulSoup
from docx import Document
import tempfile
import email
from email import policy
import extract_msg
from openai import OpenAI
import hashlib
import pickle
from functools import lru_cache
import time

# Import additional modules for ZIP and Excel processing
from data_processor import (
    process_excel_csv_from_url,
    is_excel_or_csv_url,
    answer_question_from_data
)
from zip_processor import process_zip_from_url, is_zip_url

# Import enhanced RAG components
from enhanced_retrieval import HybridRetriever
from answer_validation import AnswerValidator
from llm_reranker import LLMReranker
from langchain_integration import EnhancedDocumentProcessor, QueryRouter

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app_requests.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Create a separate logger for request/response data
request_logger = logging.getLogger('requests')
request_logger.setLevel(logging.INFO)
request_handler = logging.FileHandler('request_response.log')
request_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
request_logger.addHandler(request_handler)

app = Flask(__name__)
CORS(app)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

def check_security_sensitive_request(questions):
    """
    Check if the request contains security-sensitive information requests using GPT-4o.

    Args:
        questions (list): List of questions to check

    Returns:
        dict: Security check result with is_sensitive flag and response message
    """
    if not questions:
        return {"is_sensitive": False, "response": None}

    # Combine all questions for analysis
    combined_questions = " | ".join(questions) if isinstance(questions, list) else str(questions)

    security_check_prompt = f"""
You are a security analyzer. Analyze the following user questions to determine if they are requesting sensitive information.

SENSITIVE INFORMATION INCLUDES:
- API keys, tokens, passwords, or authentication credentials
- Personal information (SSN, credit card numbers, addresses, phone numbers)
- Security tokens, secret keys, or access codes
- Private configuration data or environment variables
- Login credentials or authentication details
- Financial account information
- Any form of sensitive technical credentials

USER QUESTIONS: {combined_questions}

Respond with ONLY "SENSITIVE" if any question requests sensitive information, or "SAFE" if all questions are safe.
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": security_check_prompt}],
            temperature=0.1,
            max_tokens=50
        )

        result = response.choices[0].message.content.strip().upper()

        if "SENSITIVE" in result:
            return {
                "is_sensitive": True,
                "response": "I cannot provide sensitive information like tokens, API keys, passwords, or personal data due to privacy and security concerns. I also cannot access external links or retrieve live data due to technical restrictions. Please obtain such information through secure, authorized channels."
            }
        else:
            return {"is_sensitive": False, "response": None}

    except Exception:
        # If security check fails, err on the side of caution for sensitive-looking keywords
        sensitive_keywords = [
            'token', 'api key', 'password', 'secret', 'credential', 'auth',
            'ssn', 'social security', 'credit card', 'phone number', 'address',
            'login', 'username', 'access code', 'private key', 'env', 'environment'
        ]

        combined_lower = combined_questions.lower()
        if any(keyword in combined_lower for keyword in sensitive_keywords):
            return {
                "is_sensitive": True,
                "response": "I cannot provide sensitive information like tokens, API keys, passwords, or personal data due to privacy and security concerns. I also cannot access external links or retrieve live data due to technical restrictions. Please obtain such information through secure, authorized channels."
            }

        return {"is_sensitive": False, "response": None}

# Initialize enhanced RAG components
hybrid_retriever = HybridRetriever(client)
answer_validator = AnswerValidator(client)
llm_reranker = LLMReranker(client)
document_processor = EnhancedDocumentProcessor()
query_router = QueryRouter()

CACHE_DIR = "cache"
os.makedirs(CACHE_DIR, exist_ok=True)

# In-memory cache for frequently accessed data using dict with size limit
memory_cache = {}
MEMORY_CACHE_SIZE = 100

# Configuration for chunking strategy
ENABLE_SEMANTIC_CHUNKING = os.getenv("ENABLE_SEMANTIC_CHUNKING", "true").lower() == "true"
SEMANTIC_CHUNKING_THRESHOLD = float(os.getenv("SEMANTIC_CHUNKING_THRESHOLD", "85.0"))
MAX_PARALLEL_WORKERS = int(os.getenv("MAX_PARALLEL_WORKERS", "3"))

# Response configuration for consistent formatting
RESPONSE_CONFIG = {
    "max_lines": 2,  # Updated to enforce 1-2 line responses
    "require_context_reference": True,
    "enforce_context_only": True,
    "max_tokens_per_response": 200  # Reduced token limit for more concise responses
}

# Parallel processing configuration
PARALLEL_CONFIG = {
    "classification_workers": 4,
    "processing_workers": 5,
    "simple_processing_workers": 4,
    "enable_performance_logging": True
}

# Embedding model configuration
EMBEDDING_CONFIG = {
    "model": "text-embedding-3-large",  # Using large model for better embedding quality
    "batch_size": 100,  # OpenAI's recommended batch size
    "max_retries": 2,
    "request_timeout": 30,
    "benefits": {
        "higher_accuracy": "Better semantic understanding and retrieval accuracy",
        "improved_context": "Enhanced context matching for complex queries",
        "better_similarity": "More precise similarity scoring for document chunks"
    }
}

# Log embedding model configuration at startup
logger.info(f"Initialized with embedding model: {EMBEDDING_CONFIG['model']}")
logger.info(f"Embedding benefits: {EMBEDDING_CONFIG['benefits']}")

def validate_and_format_response(response_text, domain="general"):
    """
    Validate and format response to ensure it meets the 1-2 line requirement with context references.

    Args:
        response_text (str): The response text to validate
        domain (str): The domain type for specific validation rules

    Returns:
        str: Formatted and validated response
    """
    if not response_text:
        return "No response generated. Please try again."

    # Split into lines and remove empty lines
    lines = [line.strip() for line in response_text.split('\n') if line.strip()]

    # Ensure response is within 1-2 lines
    if len(lines) > RESPONSE_CONFIG["max_lines"]:
        # Truncate to first 2 lines and add continuation indicator
        lines = lines[:RESPONSE_CONFIG["max_lines"]]
        if len(lines) == RESPONSE_CONFIG["max_lines"]:
            # Ensure the last line ends properly
            if not lines[-1].endswith('.'):
                lines[-1] += '.'

    # Join lines back
    formatted_response = ' '.join(lines)

    # Check for context references if required
    if RESPONSE_CONFIG["require_context_reference"]:
        context_indicators = [
            "according to", "as per", "the document", "page", "section",
            "slide", "as stated", "as shown", "the policy", "the text",
            "based on the", "from the document", "the presentation"
        ]

        has_context_ref = any(indicator in formatted_response.lower() for indicator in context_indicators)

        if not has_context_ref and domain in ["insurance", "health_policy", "history"]:
            # Add a generic context reference if missing
            formatted_response = f"According to the document, {formatted_response.lower()}"

    return formatted_response


def parallel_classify_queries(questions):
    """
    Classify multiple queries in parallel while maintaining order.

    Args:
        questions (list): List of questions to classify

    Returns:
        list: List of (question, domain) tuples in original order
    """
    def classify_single_query(question):
        try:
            domain = classify_user_query(question)
            return (question, domain)
        except Exception as e:
            # Fallback to general domain on error
            return (question, "general")

    # Process classifications in parallel while maintaining order
    max_workers = min(len(questions), PARALLEL_CONFIG["classification_workers"])

    if PARALLEL_CONFIG["enable_performance_logging"]:
        start_time = time.time()
        logger.info(f"Starting parallel classification of {len(questions)} queries with {max_workers} workers")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(classify_single_query, questions))

    if PARALLEL_CONFIG["enable_performance_logging"]:
        end_time = time.time()
        logger.info(f"Parallel classification completed in {end_time - start_time:.2f} seconds")

    return results


def parallel_process_questions_with_context(questions, index, chunks):
    """
    Process multiple questions in parallel with classification and context retrieval.

    Args:
        questions (list): List of questions to process
        index: FAISS index for similarity search
        chunks: Document chunks for context

    Returns:
        list: List of answers in original question order
    """
    # Step 1: Classify all queries in parallel
    classified_queries = parallel_classify_queries(questions)

    def process_single_question_with_context(query_data):
        question, domain = query_data
        try:
            # Route query to determine processing strategy
            routing_info = query_router.route_query(question)
            processing_hints = routing_info.get("processing_hints", {})

            # Use enhanced retrieval with hybrid search and re-ranking
            top_chunks = enhanced_insurance_retrieve(question, index, chunks, k=8, use_hybrid=True, use_reranking=True)

            # Apply query-specific filtering based on routing
            if processing_hints.get("prioritize_chunks"):
                prioritized_chunks = []
                other_chunks = []

                for chunk in top_chunks:
                    chunk_metadata = chunk.get("metadata", [])
                    if any(priority in chunk_metadata for priority in processing_hints["prioritize_chunks"]):
                        prioritized_chunks.append(chunk)
                    else:
                        other_chunks.append(chunk)

                # Combine prioritized chunks first, then others
                top_chunks = prioritized_chunks + other_chunks

            # Validate and filter context for better accuracy
            relevant_chunks = validate_context_relevance(question, top_chunks)

            # Apply smart content verification to prevent being tricked
            verification_result = smart_content_verification(question, relevant_chunks)

            # If verification fails, return "Information not provided" immediately
            if not verification_result["should_respond"]:
                return "Information not provided in the document"

            # Generate prompt based on pre-classified domain
            prompt_functions = {
                "general": build_general_prompt,
                "insurance": build_insurance_prompt,
                "health_policy": build_health_policy_prompt,
                "history": build_history_prompt
            }

            prompt_function = prompt_functions.get(domain, build_general_prompt)
            prompt = prompt_function(question, relevant_chunks)

            # Generate answer with domain-specific validation
            answer = call_gpt_fast(prompt, domain)

            # Validate answer quality
            try:
                validation_result = answer_validator.validate_answer(question, answer, relevant_chunks)

                # If answer has low confidence, try with more context
                if not validation_result.get("is_valid", True) and validation_result.get("confidence", 1.0) < 0.6:
                    # Retry with more chunks
                    fallback_chunks = insurance_specific_retrieve(question, index, chunks, k=12)
                    fallback_relevant = validate_context_relevance(question, fallback_chunks)
                    fallback_prompt = prompt_function(question, fallback_relevant)
                    answer = call_gpt_fast(fallback_prompt, domain)

            except Exception:
                # Continue with original answer if validation fails
                pass

            return answer

        except Exception as e:
            return f"Error processing question: {str(e)}"

    # Step 2: Process all questions with context in parallel while maintaining order
    max_workers = min(len(classified_queries), PARALLEL_CONFIG["processing_workers"])

    if PARALLEL_CONFIG["enable_performance_logging"]:
        start_time = time.time()
        logger.info(f"Starting parallel processing of {len(classified_queries)} questions with {max_workers} workers")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        answers = list(executor.map(process_single_question_with_context, classified_queries))

    if PARALLEL_CONFIG["enable_performance_logging"]:
        end_time = time.time()
        logger.info(f"Parallel processing completed in {end_time - start_time:.2f} seconds")

    return answers


def parallel_process_questions_simple(questions, processing_function):
    """
    Process multiple questions in parallel with classification for simple handlers.

    Args:
        questions (list): List of questions to process
        processing_function (callable): Function to process each question

    Returns:
        list: List of answers in original question order
    """
    # Step 1: Classify all queries in parallel
    classified_queries = parallel_classify_queries(questions)

    def process_with_classification(query_data):
        question, domain = query_data
        try:
            # Call the processing function with the question
            result = processing_function(question)

            # Apply response validation based on domain
            if isinstance(result, str):
                return validate_and_format_response(result, domain)
            else:
                return result

        except Exception as e:
            return f"Error processing question: {str(e)}"

    # Step 2: Process all questions in parallel while maintaining order
    max_workers = min(len(classified_queries), PARALLEL_CONFIG["simple_processing_workers"])

    if PARALLEL_CONFIG["enable_performance_logging"]:
        start_time = time.time()
        logger.info(f"Starting simple parallel processing of {len(classified_queries)} questions with {max_workers} workers")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        answers = list(executor.map(process_with_classification, classified_queries))

    if PARALLEL_CONFIG["enable_performance_logging"]:
        end_time = time.time()
        logger.info(f"Simple parallel processing completed in {end_time - start_time:.2f} seconds")

    return answers

# Adaptive chunking configuration
ADAPTIVE_CHUNKING_CONFIG = {
    "small_doc": {"max_pages": 100, "chunk_size": 800, "overlap": 100},
    "medium_doc": {"max_pages": 300, "chunk_size": 1200, "overlap": 150},
    "large_doc": {"max_pages": float('inf'), "chunk_size": 1500, "overlap": 200}
}

# Pre-compiled regex patterns for better performance
REGEX_PATTERNS = {
    'page_headers': re.compile(r"Page \d+ of \d+"),
    'whitespace': re.compile(r"\s{2,}"),
    'broken_words': re.compile(r"(\w+)-\s*\n\s*(\w+)"),
    'currency': re.compile(r"\$\s+(\d)"),
    'percentage': re.compile(r"(\d)\s+%"),
    'newlines': re.compile(r"\n{3,}"),
    'definitions': re.compile(r'\b([A-Z][\w\s]+)\s+means\s+([^\.]+\.)', re.MULTILINE),
    'first_sentence': re.compile(r'[.!?]\s'),
    'insurance_keywords': re.compile(r'\b(?:claim|premium|coverage|deductible|exclusion|benefit|policy|insured|limit|condition|amount|liability|copay|coinsurance|network|provider|reimbursement|payment|cost|fee|charge|expense|maximum|minimum|percentage|dollar|annual|monthly|eligible|eligibility|waiting|period|effective|date|termination|renewal|grace|notification|approval|document|required|submission|proof|evidence|terms|clause|diagnosis|treatment|procedure|physician|hospital|prescription|medication|emergency|preventive|specialist|definition|scope|coverage|exclusion|exception|provision|endorsement|schedule|attachment|addendum)\b', re.IGNORECASE)
}


@lru_cache(maxsize=1000)
def clean_text(text):
    # Remove page headers/footers
    text = REGEX_PATTERNS['page_headers'].sub("", text)

    # Preserve important punctuation but clean excessive whitespace
    text = REGEX_PATTERNS['whitespace'].sub(" ", text)

    # Fix broken words across lines (common in PDFs)
    text = REGEX_PATTERNS['broken_words'].sub(r"\1\2", text)

    # Preserve currency and percentage formatting
    text = REGEX_PATTERNS['currency'].sub(r"$\1", text)
    text = REGEX_PATTERNS['percentage'].sub(r"\1%", text)

    # Clean but preserve section markers
    text = REGEX_PATTERNS['newlines'].sub("\n\n", text)

    return text.strip()

@lru_cache(maxsize=1000)
def get_cache_key(url):
    return hashlib.md5(url.encode()).hexdigest()

def get_adaptive_chunk_config(page_count):
    """
    Determine optimal chunk size and overlap based on document size.

    Args:
        page_count (int): Number of pages in the document

    Returns:
        dict: Configuration with chunk_size and overlap
    """
    if page_count < 100:
        config = ADAPTIVE_CHUNKING_CONFIG["small_doc"]
        doc_type = "small"
    elif page_count < 300:
        config = ADAPTIVE_CHUNKING_CONFIG["medium_doc"]
        doc_type = "medium"
    else:
        config = ADAPTIVE_CHUNKING_CONFIG["large_doc"]
        doc_type = "large"

    return {
        "chunk_size": config["chunk_size"],
        "overlap": config["overlap"],
        "doc_type": doc_type,
        "page_count": page_count
    }

def manage_memory_cache():
    """Remove oldest items if cache exceeds size limit"""
    if len(memory_cache) > MEMORY_CACHE_SIZE:
        # Remove 20% of oldest items
        items_to_remove = len(memory_cache) - int(MEMORY_CACHE_SIZE * 0.8)
        for _ in range(items_to_remove):
            memory_cache.pop(next(iter(memory_cache)))

def save_cache(cache_key, data):
    # Save to memory cache first for faster access
    memory_cache[cache_key] = data
    manage_memory_cache()

    # Also save to disk for persistence
    try:
        with open(os.path.join(CACHE_DIR, f"{cache_key}.pkl"), "wb") as f:
            pickle.dump(data, f)
    except Exception:
        pass  # Don't fail if disk cache fails

def load_cache(cache_key):
    # Check memory cache first
    if cache_key in memory_cache:
        return memory_cache[cache_key]

    # Fall back to disk cache
    path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
    if os.path.exists(path):
        try:
            with open(path, "rb") as f:
                data = pickle.load(f)
                # Store in memory cache for next time
                memory_cache[cache_key] = data
                manage_memory_cache()
                return data
        except Exception:
            pass  # Don't fail if disk cache is corrupted
    return None


def extract_tables_from_page(page):
    """
    Extract tables from a PDF page using PyMuPDF's table detection.

    Args:
        page: PyMuPDF page object

    Returns:
        str: Formatted table text
    """
    try:
        # Find tables on the page
        tables = page.find_tables()
        table_text = ""

        for table_idx, table in enumerate(tables):
            try:
                # Extract table data
                table_data = table.extract()

                if table_data and len(table_data) > 0:
                    table_text += f"\n[TABLE {table_idx + 1}]\n"

                    # Format table with proper alignment
                    for row in table_data:
                        if row and any(cell and str(cell).strip() for cell in row):
                            # Clean and format each cell
                            formatted_row = []
                            for cell in row:
                                if cell is not None:
                                    cell_text = str(cell).strip()
                                    # Preserve currency and percentage formatting
                                    cell_text = re.sub(r'\s+', ' ', cell_text)
                                    formatted_row.append(cell_text)
                                else:
                                    formatted_row.append("")

                            # Join cells with proper spacing
                            table_text += " | ".join(formatted_row) + "\n"

                    table_text += "[END TABLE]\n"

            except Exception:
                # If table extraction fails, continue with next table
                continue

        return table_text

    except Exception:
        # If no tables found or extraction fails, return empty string
        return ""


def extract_enhanced_text_from_page(page):
    """
    Extract both regular text and tables from a PDF page.

    Args:
        page: PyMuPDF page object

    Returns:
        str: Combined text and table content
    """
    # Get regular text
    regular_text = page.get_text()

    # Get table content
    table_text = extract_tables_from_page(page)

    # Combine text and tables
    if table_text:
        combined_text = regular_text + "\n\n" + table_text
    else:
        combined_text = regular_text

    return clean_text(combined_text)


def identify_key_sections(text_by_page):
    """
    Identify and extract key sections from insurance documents.

    Args:
        text_by_page: List of text content by page

    Returns:
        dict: Dictionary containing identified sections
    """
    sections = {
        "permanent_exclusions": [],
        "waiting_periods": [],
        "maternity_benefits": [],
        "general_exclusions": [],
        "coverage_details": []
    }

    # Section identification patterns
    section_patterns = {
        "permanent_exclusions": [
            r"permanent\s+exclusion", r"permanently\s+excluded",
            r"not\s+covered\s+under\s+any\s+circumstances",
            r"exclusions\s+that\s+apply\s+throughout"
        ],
        "waiting_periods": [
            r"waiting\s+period", r"waiting\s+time",
            r"moratorium\s+period", r"initial\s+waiting"
        ],
        "maternity_benefits": [
            r"maternity\s+benefit", r"maternity\s+coverage",
            r"pregnancy\s+related", r"childbirth\s+expenses"
        ]
    }

    # Specific condition patterns to look for
    condition_patterns = {
        "arthritis": r"\barthritis\b",
        "abortion": r"\babortion\b",
        "hydrocele": r"\bhydrocele\b"
    }

    for page_num, page_text in enumerate(text_by_page, 1):
        page_text_lower = page_text.lower()

        # Check each section type
        for section_name, patterns in section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, page_text_lower):
                    # Found a relevant section, extract surrounding context
                    section_info = {
                        "page": page_num,
                        "text": page_text,
                        "section_type": section_name,
                        "conditions_found": []
                    }

                    # Check for specific conditions in this section
                    for condition, cond_pattern in condition_patterns.items():
                        if re.search(cond_pattern, page_text_lower):
                            section_info["conditions_found"].append(condition)

                    sections[section_name].append(section_info)
                    break  # Avoid duplicate entries for the same page

    return sections


def cross_check_specific_conditions(sections, question):
    """
    Cross-check specific conditions mentioned in the question against identified sections.

    Args:
        sections: Dictionary of identified sections
        question: User's question

    Returns:
        dict: Cross-check results with relevant sections
    """
    question_lower = question.lower()

    # Identify what the user is asking about
    conditions_in_question = []
    if re.search(r'\barthritis\b', question_lower):
        conditions_in_question.append("arthritis")
    if re.search(r'\babortion\b', question_lower):
        conditions_in_question.append("abortion")
    if re.search(r'\bhydrocele\b', question_lower):
        conditions_in_question.append("hydrocele")

    # Identify what type of information they want
    info_type = None
    if re.search(r'exclusion|excluded|not covered', question_lower):
        info_type = "exclusions"
    elif re.search(r'waiting|period|moratorium', question_lower):
        info_type = "waiting_periods"
    elif re.search(r'maternity|pregnancy|childbirth', question_lower):
        info_type = "maternity_benefits"

    # Cross-check results
    cross_check_results = {
        "conditions_asked": conditions_in_question,
        "info_type_requested": info_type,
        "relevant_sections": [],
        "specific_findings": []
    }

    # Find relevant sections
    if info_type:
        section_key = "permanent_exclusions" if info_type == "exclusions" else info_type
        if section_key in sections:
            for section in sections[section_key]:
                # Check if any of the asked conditions are mentioned in this section
                for condition in conditions_in_question:
                    if condition in section["conditions_found"]:
                        cross_check_results["relevant_sections"].append(section)
                        cross_check_results["specific_findings"].append(
                            f"{condition.title()} found in {section['section_type']} on page {section['page']}"
                        )

    return cross_check_results


def extract_text_from_url(url):
    # Ensure URL is properly formatted and encoded
    try:
        # Try to fetch the document
        response = requests.get(url, timeout=60)
        response.raise_for_status()  # Raise exception for 4xx/5xx responses
        content_type = response.headers.get("Content-Type", "").lower()

        # Extract filename from URL, handling query parameters
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        if path_parts and path_parts[-1]:
            filename = path_parts[-1].lower()
        else:
            # Use a default filename if path doesn't have one
            filename = "document.pdf" if "pdf" in content_type else "document"

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(response.content)
            tmp_path = tmp.name
    except Exception as e:
        raise e

    if "pdf" in content_type or filename.endswith(".pdf"):
        doc = fitz.open(tmp_path)
        # Use enhanced text extraction that includes tables
        return [extract_enhanced_text_from_page(page) for page in doc]

    elif "word" in content_type or filename.endswith(".docx"):
        doc = Document(tmp_path)
        return ["\n".join(clean_text(p.text) for p in doc.paragraphs)]

    elif "text/plain" in content_type or filename.endswith(".txt"):
        return [clean_text(response.text)]

    elif "html" in content_type or filename.endswith(".html"):
        soup = BeautifulSoup(response.text, "lxml")
        return [clean_text(soup.get_text(separator="\n"))]

    elif filename.endswith(".eml"):
        with open(tmp_path, "rb") as f:
            msg = email.message_from_binary_file(f, policy=policy.default)
        body = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    body += part.get_payload(decode=True).decode(errors="ignore")
        else:
            body = msg.get_payload(decode=True).decode(errors="ignore")
        return [clean_text(body)]

    elif filename.endswith(".msg"):
        msg = extract_msg.Message(tmp_path)
        return [clean_text(msg.body)]

    elif filename.endswith((".jpg", ".jpeg", ".png")) or any(img_type in content_type for img_type in ["image/jpeg", "image/jpg", "image/png"]):
        # For images, return a placeholder text that indicates it's an image
        # The actual image processing will be handled by handle_image_file function
        return ["[IMAGE FILE - Content will be analyzed using vision AI]"]

    else:
        raise ValueError("Unsupported document type or unknown format.")


def process_page_semantic_chunk(page_data):
    """
    Process a single page using semantic chunking with adaptive sizing.
    This function is designed to be used with parallel processing.
    """
    page_num, page_text, embeddings_model, chunk_config = page_data

    if not page_text.strip():
        return []

    try:
        # Determine minimum chunk size based on document size
        min_chunk_size = max(100, chunk_config["chunk_size"] // 8)  # Adaptive minimum size

        # Create semantic chunker for this page with adaptive configuration
        semantic_splitter = SemanticChunker(
            embeddings_model,
            breakpoint_threshold_type="percentile",
            breakpoint_threshold_amount=SEMANTIC_CHUNKING_THRESHOLD,  # Configurable threshold
            min_chunk_size=min_chunk_size  # Adaptive minimum chunk size
        )

        # Split the page text semantically
        semantic_chunks = semantic_splitter.split_text(page_text)

        # Process each semantic chunk
        processed_chunks = []
        for i, chunk in enumerate(semantic_chunks):
            if len(chunk.strip()) < 50:
                continue

            # Add context prefix for better identification
            first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
            first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]
            context_prefix = f"Page {page_num}, Semantic Section {i+1}: {first_sentence}... "

            # Analyze chunk content for metadata
            chunk_lower = chunk.lower()
            contains_definition = 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower))
            contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower))
            contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower))
            contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower))
            contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower))

            metadata = []
            if contains_definition: metadata.append("definition")
            if contains_exclusion: metadata.append("exclusion")
            if contains_coverage: metadata.append("coverage")
            if contains_limit: metadata.append("limit")
            if contains_condition: metadata.append("condition")

            processed_chunks.append({
                "text": context_prefix + chunk,
                "page": page_num,
                "section": i+1,
                "raw_text": chunk,
                "metadata": metadata,
                "chunk_type": "semantic",
                "chunk_config": chunk_config,
                "actual_size": len(chunk)
            })

        return processed_chunks

    except Exception:
        # Fallback to recursive chunking for this page
        return process_page_recursive_chunk((page_num, page_text, chunk_config))


def process_page_recursive_chunk(page_data):
    """
    Fallback function for recursive chunking of a single page with adaptive sizing.
    """
    if len(page_data) == 3:
        page_num, page_text, chunk_config = page_data
    else:
        # Backward compatibility for old calls
        page_num, page_text = page_data
        chunk_config = {"chunk_size": 800, "overlap": 100, "doc_type": "default"}

    if not page_text.strip():
        return []

    # Enhanced insurance-specific separators for better boundary detection
    separators = [
        # Document structure markers (highest priority)
        "\n\nARTICLE", "\n\nSECTION", "\n\nCLAUSE", "\n\nPART",
        "\n\nCOVERAGE", "\n\nBENEFIT", "\n\nEXCLUSION", "\n\nLIMIT",
        "\n\nArticle", "\n\nSection", "\n\nClause", "\n\nPart",
        "\n\nCoverage", "\n\nBenefit", "\n\nExclusion", "\n\nLimit",

        # Numbered sections (common in legal documents)
        r"\n\d+\.\d+", r"\n\d+\.", r"\n[A-Z]\.", r"\n[a-z]\.", r"\n[ivxIVX]+\.",

        # Definitions and key terms
        "\n\nDefinitions", "\n\nTerms", "\n\nGlossary",

        # General document separators
        "\n\n", "\n", ". ", "; ", ", ", " "
    ]

    # Use adaptive chunk size and overlap from configuration
    chunk_size = chunk_config["chunk_size"]
    chunk_overlap = chunk_config["overlap"]

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=separators
    )

    page_chunks = splitter.split_text(page_text)
    processed_chunks = []

    for i, chunk in enumerate(page_chunks):
        if len(chunk.strip()) < 50:
            continue

        # Add context prefix
        first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
        first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]
        context_prefix = f"Page {page_num}, Section {i+1}: {first_sentence}... "

        # Analyze chunk content for metadata
        chunk_lower = chunk.lower()
        contains_definition = 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower))
        contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower))
        contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower))
        contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower))
        contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower))

        metadata = []
        if contains_definition: metadata.append("definition")
        if contains_exclusion: metadata.append("exclusion")
        if contains_coverage: metadata.append("coverage")
        if contains_limit: metadata.append("limit")
        if contains_condition: metadata.append("condition")

        processed_chunks.append({
            "text": context_prefix + chunk,
            "page": page_num,
            "section": i+1,
            "raw_text": chunk,
            "metadata": metadata,
            "chunk_type": "recursive",
            "chunk_config": chunk_config,
            "actual_size": len(chunk)
        })

    return processed_chunks


def generate_smart_chunks(text_by_page):
    """
    Generate smart chunks using parallel semantic chunking with adaptive sizing.
    Falls back to enhanced document processing and then recursive chunking if needed.
    """
    # Determine adaptive chunk configuration based on document size
    page_count = len([page for page in text_by_page if page.strip()])
    chunk_config = get_adaptive_chunk_config(page_count)

    try:
        # First, try enhanced LangChain-based processing with adaptive chunking
        enhanced_chunks = document_processor.process_documents(text_by_page, chunk_config)
        if enhanced_chunks:
            return enhanced_chunks
    except Exception:
        pass

    # Try parallel semantic chunking (if enabled)
    if ENABLE_SEMANTIC_CHUNKING:
        try:


            # Create embeddings model for semantic chunking
            embeddings_model = OpenAIEmbeddings(
                model=EMBEDDING_CONFIG["model"],  # Use large model for better embedding quality
                openai_api_key=os.getenv("OPENAI_API_KEY"),
                max_retries=EMBEDDING_CONFIG["max_retries"],
                request_timeout=EMBEDDING_CONFIG["request_timeout"]
            )

            # Filter out empty pages and prepare data for parallel processing with chunk config
            valid_pages = [(page_num, page_text, embeddings_model, chunk_config)
                           for page_num, page_text in enumerate(text_by_page, 1)
                           if page_text.strip() and len(page_text.strip()) > 50]

            if not valid_pages:
                return []

            # Determine optimal number of workers based on configuration and system resources
            max_workers = min(len(valid_pages), MAX_PARALLEL_WORKERS, os.cpu_count() or 1)

            all_chunks = []

            # Use ThreadPoolExecutor for I/O-bound embedding operations
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Process pages in parallel
                chunk_results = list(executor.map(process_page_semantic_chunk, valid_pages))

                # Flatten results
                for page_chunks in chunk_results:
                    if page_chunks:
                        all_chunks.extend(page_chunks)

            if all_chunks:
                return all_chunks

        except Exception:
            pass

    # Final fallback to parallel recursive chunking with adaptive sizing
    try:
        page_data = [(page_num, page_text, chunk_config)
                     for page_num, page_text in enumerate(text_by_page, 1)
                     if page_text.strip() and len(page_text.strip()) > 50]

        if not page_data:
            return []

        max_workers = min(len(page_data), 4, os.cpu_count() or 1)
        all_chunks = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            chunk_results = list(executor.map(process_page_recursive_chunk, page_data))

            for page_chunks in chunk_results:
                all_chunks.extend(page_chunks)

        return all_chunks

    except Exception:
        return []


def embed_chunks_openai(chunks):
    texts = [c["text"] for c in chunks]

    try:
        # Process in batches to avoid API limits and improve efficiency
        batch_size = EMBEDDING_CONFIG["batch_size"]
        all_embeddings = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            resp = client.embeddings.create(model=EMBEDDING_CONFIG["model"], input=batch_texts)
            batch_embeddings = [d.embedding for d in resp.data]
            all_embeddings.extend(batch_embeddings)

        embeddings = np.array(all_embeddings, dtype=np.float32)
        norm_embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]

        # Build FAISS index with optimized settings
        index = faiss.IndexFlatIP(norm_embeddings.shape[1])
        index.add(norm_embeddings)

        # Build hybrid retrieval indices
        try:
            hybrid_retriever.build_indices(chunks)
        except Exception:
            # Silently continue if hybrid indices fail
            pass

        return index, chunks, norm_embeddings
    except Exception as e:
        raise e


def insurance_specific_retrieve(question, index, chunks, k=8):
    # Query embedding with same model
    try:
        q_resp = client.embeddings.create(model=EMBEDDING_CONFIG["model"], input=question)
        q_vec = np.array(q_resp.data[0].embedding, dtype=np.float32)
        q_vec = q_vec / np.linalg.norm(q_vec)
    except Exception as e:
        raise e

    # Get more candidates first (triple the amount for better matching)
    scores, indices = index.search(np.array([q_vec]), k*4)

    # Pre-defined insurance keywords set for faster lookup
    insurance_keywords = {
        'claim', 'premium', 'coverage', 'deductible', 'exclusion',
        'benefit', 'policy', 'insured', 'limit', 'condition', 'amount',
        'liability', 'copay', 'coinsurance', 'network', 'provider',
        'reimbursement', 'payment', 'cost', 'fee', 'charge', 'expense',
        'maximum', 'minimum', 'percentage', 'dollar', 'annual', 'monthly',
        'eligible', 'eligibility', 'waiting', 'period', 'effective', 'date',
        'termination', 'renewal', 'grace', 'notification', 'approval', 'document',
        'required', 'submission', 'proof', 'evidence', 'terms', 'clause',
        'diagnosis', 'treatment', 'procedure', 'physician', 'hospital',
        'prescription', 'medication', 'emergency', 'preventive', 'specialist',
        'definition', 'scope', 'coverage', 'exclusion', 'exception', 'provision',
        'endorsement', 'schedule', 'attachment', 'addendum'
    }

    # Extract key terms from question (optimized)
    question_lower = question.lower()
    question_words = set(re.findall(r"\w+", question_lower))
    question_words_list = list(question_words)
    question_bigrams = set([' '.join(pair) for pair in zip(question_words_list, question_words_list[1:])])

    # Pre-compute question characteristics for efficiency
    question_has_numbers = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit', question_lower))
    is_definition_question = any(word in question_lower for word in ['what is', 'define', 'meaning'])

    top_matches = []
    for rank, i in enumerate(indices[0]):
        chunk = chunks[i]
        raw_text = chunk["raw_text"]
        text_lower = raw_text.lower()

        # Optimized keyword extraction and matching
        text_words = set(re.findall(r"\w+", text_lower))
        common_keywords = question_words & text_words
        insurance_terms = common_keywords & insurance_keywords

        # Check for exact phrase matches (optimized)
        phrase_matches = sum(1 for word in question_words
                           if len(word) > 3 and word in text_lower)

        # Check for bigram matches (optimized)
        bigram_matches = sum(3 for bigram in question_bigrams if bigram in text_lower)

        # Check for numerical/financial content matches
        has_numbers = bool(re.search(r'\$|%|\d+', raw_text))
        number_bonus = 0.15 if (has_numbers and question_has_numbers) else 0

        # Look for definition patterns ("X means Y")
        definition_pattern = 'means' in text_lower and bool(re.search(r'\b\w+\s+means\b', text_lower))
        definition_bonus = 0.2 if (definition_pattern and is_definition_question) else 0

        keyword_score = len(common_keywords) + (len(insurance_terms) * 2) + phrase_matches + bigram_matches
        semantic_score = scores[0][rank]

        # Enhanced scoring formula
        final_score = (0.7 * semantic_score) + \
                      (0.15 * min(keyword_score / 10.0, 1.0)) + \
                      number_bonus + \
                      definition_bonus

        top_matches.append({
            "text": chunk["text"],
            "raw_text": raw_text,
            "page": chunk["page"],
            "score": final_score
        })

    top_matches = sorted(top_matches, key=lambda x: x["score"], reverse=True)
    result = top_matches[:k]

    return result


def enhanced_insurance_retrieve(question, index, chunks, k=8, use_hybrid=True, use_reranking=True):
    """
    Enhanced retrieval using hybrid search and LLM re-ranking.

    Args:
        question: User question
        index: FAISS index
        chunks: Document chunks
        k: Number of results to return
        use_hybrid: Whether to use hybrid search
        use_reranking: Whether to use LLM re-ranking

    Returns:
        Enhanced retrieval results
    """
    try:
        if use_hybrid and hasattr(hybrid_retriever, 'faiss_index') and hybrid_retriever.faiss_index is not None:
            # Use hybrid retrieval (FAISS + BM25)
            hybrid_results = hybrid_retriever.hybrid_search(question, k * 2)

            # Convert to expected format
            top_matches = []
            for result in hybrid_results:
                top_matches.append({
                    "text": result["text"],
                    "raw_text": result.get("raw_text", result["text"]),
                    "page": result.get("page", 1),
                    "score": result["combined_score"],
                    "semantic_score": result.get("semantic_score", 0.0),
                    "keyword_score": result.get("keyword_score", 0.0)
                })
        else:
            # Fallback to original retrieval
            top_matches = insurance_specific_retrieve(question, index, chunks, k * 2)

        if use_reranking and top_matches:
            # Apply LLM re-ranking
            reranked_results = llm_reranker.rerank_chunks(question, top_matches, k)
            return reranked_results
        else:
            return top_matches[:k]

    except Exception:
        # Fallback to original method on error
        return insurance_specific_retrieve(question, index, chunks, k)


def smart_content_verification(question, context_chunks):
    """
    Enhanced verification to prevent the LLM from being tricked by misleading documents.

    Args:
        question (str): The user's question
        context_chunks (list): Context chunks from the document

    Returns:
        dict: Verification results with confidence score and warnings
    """
    question_lower = question.lower()

    # Extract key terms from the question
    question_words = set(re.findall(r'\w+', question_lower))

    # Remove common stop words to focus on meaningful terms
    stop_words = {'is', 'are', 'what', 'how', 'when', 'where', 'why', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    meaningful_words = question_words - stop_words

    verification_results = {
        "has_direct_match": False,
        "confidence_score": 0.0,
        "matching_chunks": [],
        "warnings": [],
        "should_respond": False
    }

    for chunk in context_chunks:
        chunk_text = chunk.get('raw_text', '').lower()
        chunk_words = set(re.findall(r'\w+', chunk_text))

        # Check for direct word matches
        word_matches = meaningful_words & chunk_words
        match_ratio = len(word_matches) / max(len(meaningful_words), 1)

        # Look for exact phrase matches (more reliable)
        exact_matches = 0
        for word in meaningful_words:
            if len(word) > 3 and word in chunk_text:
                exact_matches += 1

        # Calculate confidence based on matches
        chunk_confidence = (match_ratio * 0.6) + (exact_matches / max(len(meaningful_words), 1) * 0.4)

        if chunk_confidence > 0.3:  # Threshold for considering a chunk relevant
            verification_results["matching_chunks"].append({
                "chunk": chunk,
                "confidence": chunk_confidence,
                "word_matches": word_matches
            })

    # Overall verification
    if verification_results["matching_chunks"]:
        # Sort by confidence
        verification_results["matching_chunks"].sort(key=lambda x: x["confidence"], reverse=True)
        verification_results["confidence_score"] = verification_results["matching_chunks"][0]["confidence"]

        # Check if we have high confidence matches
        if verification_results["confidence_score"] > 0.6:
            verification_results["has_direct_match"] = True
            verification_results["should_respond"] = True
        elif verification_results["confidence_score"] > 0.4:
            verification_results["warnings"].append("Moderate confidence - verify answer carefully")
            verification_results["should_respond"] = True
        else:
            verification_results["warnings"].append("Low confidence - likely misleading content")
            verification_results["should_respond"] = False
    else:
        verification_results["warnings"].append("No relevant content found for this question")
        verification_results["should_respond"] = False

    return verification_results


def validate_context_relevance(question, context_chunks, min_relevance=0.25):
    """Enhanced filtering of context chunks based on semantic relevance"""
    question_words = set(re.findall(r'\w+', question.lower()))

    # Identify key question types to prioritize different content
    is_definition_question = bool(re.search(r'\bwhat is|\bdefine|\bmeaning|\bdefin[ei]|\bconcept', question.lower()))
    is_coverage_question = bool(re.search(r'\bcover|\bprovide|\binclude|\beligible', question.lower()))
    is_exclusion_question = bool(re.search(r'\bexclude|\bnot cover|\bdeny|\breject', question.lower()))
    is_process_question = bool(re.search(r'\bhow|\bprocess|\bprocedure|\bsteps|\bsubmit', question.lower()))
    is_document_question = bool(re.search(r'\bdocument|\bproof|\bevidence|\breceipt|\bform', question.lower()))
    is_limit_question = bool(re.search(r'\blimit|\bmaximum|\bminimum|\bcap|\bceiling|\bamount', question.lower()))

    relevant_chunks = []
    for chunk in context_chunks:
        chunk_words = set(re.findall(r'\w+', chunk['raw_text'].lower()))
        chunk_text = chunk['raw_text'].lower()

        # Basic overlap score
        overlap = len(question_words & chunk_words)
        relevance = overlap / max(len(question_words), 1)

        # Content type bonuses
        metadata = chunk.get('metadata', [])

        # Give bonuses to chunks that match the question type
        type_bonus = 0
        if is_definition_question and ('definition' in metadata or bool(re.search(r'\bmeans\b|\bis defined as\b', chunk_text))):
            type_bonus += 0.3
        if is_coverage_question and ('coverage' in metadata or bool(re.search(r'\bcovered\b|\beligible\b', chunk_text))):
            type_bonus += 0.3
        if is_exclusion_question and ('exclusion' in metadata or bool(re.search(r'\bexcluded\b|\bnot covered\b', chunk_text))):
            type_bonus += 0.3
        if is_process_question and bool(re.search(r'\bsteps\b|\bprocess\b|\bprocedure\b', chunk_text)):
            type_bonus += 0.3
        if is_document_question and bool(re.search(r'\bdocument\b|\bproof\b|\bevidence\b|\bform\b', chunk_text)):
            type_bonus += 0.3
        if is_limit_question and ('limit' in metadata or bool(re.search(r'\blimit\b|\bmaximum\b|\bcap\b', chunk_text))):
            type_bonus += 0.3

        # Final relevance score with bonus
        final_relevance = relevance + type_bonus

        if final_relevance >= min_relevance or len(relevant_chunks) < 2:  # Always keep at least 2
            relevant_chunks.append({
                **chunk,
                "relevance_score": final_relevance
            })

    # Sort by relevance score and limit
    relevant_chunks = sorted(relevant_chunks, key=lambda x: x.get("relevance_score", 0), reverse=True)
    return relevant_chunks[:7]  # Limit to top 7 most relevant for more context


# Query Classification Function
def classify_user_query(question):
    """
    Classify user query into one of the predefined categories using OpenAI GPT-4o-mini.

    Args:
        question (str): The user's question

    Returns:
        str: The classified category
    """
    classification_prompt = f"""
You are a query classifier. Classify the following user question into one of these categories:

1. general - General knowledge questions not specific to any domain
2. insurance - Insurance policy, claims, coverage, premium related questions
3. health_policy - Health insurance, medical coverage, health benefits related questions
4. history - Historical events, dates, figures, civilizations, and historical facts

Question: {question}

Respond with only the category name (general, insurance, health_policy, or history).
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": classification_prompt}],
            temperature=0.1,
            max_tokens=50
        )

        classification = response.choices[0].message.content.strip().lower()

        # Validate the classification
        valid_categories = ["general", "insurance", "health_policy", "history"]
        if classification in valid_categories:
            return classification
        else:
            # Default to general if classification is invalid
            return "general"

    except Exception:
        # Default to general on error
        return "general"


# Specialized Prompt Functions
def detect_irrelevant_question(question, context_chunks=None):
    """
    Detect if a question is irrelevant or inappropriate for the given context.

    Args:
        question (str): The user's question
        context_chunks (list, optional): Context chunks from the document (unused)

    Returns:
        bool: True if question is irrelevant/inappropriate
    """
    # context_chunks parameter kept for compatibility but not used
    question_lower = question.lower()

    # Check for clearly inappropriate/illegal topics
    inappropriate_patterns = [
        r'\bfraud\b', r'\bscam\b', r'\bcheat\b',
        r'\bhack\b', r'\bsteal\b', r'\bfake\b', r'\bforge\b',
        r'\bmanipulate\b', r'\bdeceive\b', r'\bmisrepresent\b',
        r'\babuse\b', r'\bexploit\b'
    ]

    # Check if question contains inappropriate patterns
    for pattern in inappropriate_patterns:
        if re.search(pattern, question_lower):
            return True

    # Don't check for context overlap for legitimate insurance/policy questions
    # as they might use different terminology than the context
    return False


def analyze_context_specificity(question, context_chunks):
    """
    Analyze if the context contains specific information that should be preferred over general knowledge.

    Args:
        question (str): The user's question
        context_chunks (list): Context chunks from the document

    Returns:
        dict: Analysis results with specificity indicators
    """
    question_lower = question.lower()
    context_text = " ".join([chunk.get("text", "") for chunk in context_chunks]).lower()

    # Check for specific product/model mentions
    specific_indicators = {
        "has_specific_product": False,
        "has_specific_model": False,
        "has_specific_brand": False,
        "specific_terms": [],
        "context_relevance": "low"
    }

    # Look for specific product patterns in question
    specific_patterns = [
        r'\b(splendor|hero|honda|yamaha|bajaj)\s+(bike|motorcycle)',
        r'\b(iphone|samsung|oneplus|xiaomi)\s+(phone|mobile)',
        r'\b(maruti|hyundai|tata|mahindra)\s+(car|vehicle)',
        r'\b(model|variant|version)\s+\w+',
        r'\b\w+\s+(series|edition|pro|max|plus)\b'
    ]

    for pattern in specific_patterns:
        matches = re.findall(pattern, question_lower)
        if matches:
            specific_indicators["has_specific_product"] = True
            specific_indicators["specific_terms"].extend(matches)

    # Check if context contains the specific terms mentioned in question
    question_words = set(re.findall(r'\w+', question_lower))
    context_words = set(re.findall(r'\w+', context_text))

    # Calculate relevance based on overlap
    overlap = len(question_words & context_words)
    total_question_words = len(question_words)

    if total_question_words > 0:
        relevance_ratio = overlap / total_question_words
        if relevance_ratio > 0.6:
            specific_indicators["context_relevance"] = "high"
        elif relevance_ratio > 0.3:
            specific_indicators["context_relevance"] = "medium"

    # Check for specific terms in context
    for term in specific_indicators["specific_terms"]:
        term_words = term if isinstance(term, tuple) else [term]
        if any(word in context_text for word in term_words):
            specific_indicators["has_specific_model"] = True
            break

    return specific_indicators


def detect_completely_false_information(question, context_chunks):
    """
    Detect if context contains completely false basic facts.

    Args:
        question (str): The user's question
        context_chunks (list): Context chunks from the document

    Returns:
        dict: False information detection results
    """
    context_text = " ".join([chunk.get("text", "") for chunk in context_chunks]).lower()
    question_lower = question.lower()

    # Known false information patterns
    false_patterns = {
        "geography": [
            (r"capital.*france.*london", "The capital of France is Paris, not London"),
            (r"capital.*india.*mumbai", "The capital of India is New Delhi, not Mumbai"),
            (r"capital.*usa.*new york", "The capital of USA is Washington D.C., not New York")
        ],
        "science": [
            (r"water.*boil.*50|water.*boil.*30", "Water boils at 100°C (212°F) at sea level, not at lower temperatures"),
            (r"earth.*flat", "The Earth is spherical, not flat"),
            (r"sun.*revolve.*earth", "The Earth revolves around the Sun, not vice versa")
        ],
        "basic_facts": [
            (r"7.*continent.*8|8.*continent.*7", "There are 7 continents, not 8"),
            (r"24.*hour.*25|25.*hour.*24", "A day has 24 hours, not 25")
        ]
    }

    false_info_detected = {
        "has_false_info": False,
        "false_categories": [],
        "corrections": []
    }

    for category, patterns in false_patterns.items():
        for pattern, correction in patterns:
            if re.search(pattern, context_text) and any(word in question_lower for word in pattern.split('.*')):
                false_info_detected["has_false_info"] = True
                false_info_detected["false_categories"].append(category)
                false_info_detected["corrections"].append(correction)

    return false_info_detected


def build_general_prompt(question, context_chunks):
    """Build prompt for general questions that strictly uses only the provided context."""

    # Check for irrelevant questions
    if detect_irrelevant_question(question, context_chunks):
        return f"""
You are a helpful assistant. The user has asked an inappropriate or irrelevant question.

QUESTION: {question}

Respond with strong, clear language about the inappropriateness. Respond in JSON format:
{{ "answer": "This question is inappropriate and cannot be answered. Please ask relevant questions about the document content." }}
"""

    context = "\n---\n".join([c["text"] for c in context_chunks])

    return f"""
You are a helpful assistant that answers questions STRICTLY based on the provided context.

CRITICAL REQUIREMENTS:
- Answer ONLY using information found in the provided context
- DO NOT use your general knowledge or external information
- VERIFY that the question is DIRECTLY answered in the document - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated document content
- ONLY answer if there is a CLEAR, DIRECT match between the question and document content
- Provide EXTREMELY CONCISE responses (1-2 lines maximum)
- Always reference the specific part of the context you're using (e.g., "According to page X" or "As stated in the document")
- Use plain, everyday language instead of technical jargon
- Keep answers brief and to the point

RESPONSE GUIDELINES:
- BEFORE answering, verify the question is DIRECTLY addressed in the document
- If information is clearly and explicitly available: "According to the document, [brief answer with specific details from context]"
- If information is vague, unclear, or requires inference: "Information not provided in the document"
- If the document contains misleading or unrelated content: "Information not provided in the document"
- If the question asks about something not explicitly mentioned: "Information not provided in the document"
- DO NOT make assumptions or connect unrelated information
- DO NOT give advice, suggestions, or general guidance when information is missing
- DO NOT tell users to "refer to other sources" or "contact someone" - just state what's missing

VERIFICATION CHECKLIST:
1. Is the exact question topic explicitly mentioned in the document?
2. Is the specific information requested clearly stated (not implied)?
3. Can I point to the exact sentence/section that answers this question?
4. Am I making any assumptions or inferences?
If ANY answer is NO, respond with "Information not provided in the document"

CONTEXT FROM DOCUMENT:
{context}

QUESTION: {question}

Answer strictly based on the context provided above. Include specific references to the document. Keep your response to 1-2 lines maximum. Respond in JSON format:
{{ "answer": "..." }}
"""


def build_insurance_prompt(question, context_chunks):
    """
    Builds a prompt that instructs the LLM to answer as a knowledgeable insurance assistant,
    using only the provided context, and replying in a clear, concise tone while extracting
    complete financial or legal clauses if present.

    Parameters:
        question (str): The user's question.
        context_chunks (list of dict): List of context dictionaries with a 'text' key.

    Returns:
        str: The formatted prompt to send to the LLM.
    """

    # Check for irrelevant questions
    if detect_irrelevant_question(question, context_chunks):
        return f"""
You are an insurance assistant. The user has asked an inappropriate question.

QUESTION: {question}

Respond with strong language about fraud and illegal activities. Respond in JSON format:
{{ "answer": "Fraud is illegal and voids policy coverage. Such activities are strictly prohibited and will result in claim denial and policy cancellation." }}
"""

    context = "\n---\n".join([c["text"] for c in context_chunks])

    # Add cross-checking instructions for specific conditions
    cross_check_instructions = ""
    question_lower = question.lower()

    if any(condition in question_lower for condition in ['arthritis', 'abortion', 'hydrocele']):
        cross_check_instructions = """
SPECIAL CROSS-CHECKING REQUIREMENTS:
- For ARTHRITIS: Check both "Permanent Exclusions" and "Waiting Periods" sections
- For ABORTION: Check "Permanent Exclusions" and "Maternity Benefits" sections
- For HYDROCELE: Check "Waiting Periods" table specifically
- Always mention the specific section where information is found
- If condition appears in multiple sections, reference all relevant sections
"""

    return f"""
You are a knowledgeable insurance assistant that answers STRICTLY based on the provided policy document.

CRITICAL REQUIREMENTS:
- Answer ONLY using information found in the provided policy context
- DO NOT use general insurance knowledge or external information
- VERIFY that the question is DIRECTLY answered in the policy - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated policy content
- ONLY answer if there is a CLEAR, DIRECT match between the question and policy content
- Provide EXTREMELY CONCISE responses (1-2 lines maximum)
- Use plain, everyday language instead of technical insurance jargon
- Extract exact figures (₹ amounts, percentages, waiting periods) directly from the document
- Always reference specific sections, pages, or tables where information is found
- If information is not in the context, clearly state "[Specific detail] is not provided in the policy document"
- Cross-check multiple sections for comprehensive coverage information
- Never add information not found in the context
- Keep answers brief and to the point
- DO NOT give advice or suggestions when information is missing - just state what's not provided

SMART VERIFICATION PROCESS:
1. Does the policy explicitly mention the exact topic being asked about?
2. Is the specific information clearly stated (not implied or suggested)?
3. Can I quote the exact sentence that answers this question?
4. Am I making any assumptions based on similar but different information?
If ANY answer is NO, respond with "Information not provided in the policy document"

{cross_check_instructions}

RESPONSE STYLE EXAMPLES:
- "According to page 15, arthritis is permanently excluded with a 2-year waiting period for joint conditions."
- "As per page 8, abortion is covered under maternity benefits with a ₹25,000 limit, excluding elective procedures."
- "The policy specifies a 1-year waiting period for hydrocele with ₹50,000 coverage per incident."
- "Flight number is not provided in the policy document."
- "Premium payment dates are not specified in the document."

EXAMPLES OF SMART VERIFICATION:
- Question: "What is the coverage for dental treatment?" → If policy only mentions "medical treatment" → "Information not provided in the policy document"
- Question: "What is the premium amount?" → If policy only mentions "premium payment" without amount → "Information not provided in the policy document"
- Question: "Is cancer covered?" → If policy only mentions "critical illness" without specifying cancer → "Information not provided in the policy document"

CONTEXT FROM POLICY DOCUMENT:
{context}

QUESTION: {question}

Answer strictly based on the policy document provided above. Include specific page/section references. Keep your response to 1-2 lines maximum. Respond in JSON format:
{{ "answer": "..." }}
"""


def build_health_policy_prompt(question, context_chunks):
    """Build prompt for health policy related questions."""

    # Check for irrelevant questions
    if detect_irrelevant_question(question, context_chunks):
        return f"""
You are a health insurance specialist. The user has asked an inappropriate question.

QUESTION: {question}

Respond with strong language about fraud and illegal activities. Respond in JSON format:
{{ "answer": "Fraud is illegal and voids policy coverage. Such activities violate insurance regulations and will result in immediate policy termination." }}
"""

    context = "\n---\n".join([c["text"] for c in context_chunks])

    # Add cross-checking instructions for health-specific conditions
    cross_check_instructions = ""
    question_lower = question.lower()

    if any(condition in question_lower for condition in ['arthritis', 'abortion', 'hydrocele', 'maternity', 'pregnancy']):
        cross_check_instructions = """
SPECIAL HEALTH POLICY CROSS-CHECKING:
- For ARTHRITIS: Check "Permanent Exclusions" and "Waiting Periods" for joint conditions
- For ABORTION: Cross-reference "Maternity Benefits" and "Permanent Exclusions" sections
- For HYDROCELE: Verify coverage in "Waiting Periods" table and surgical benefits
- For MATERNITY: Check exclusions like IVF, surrogacy in maternity benefits section
- Always specify which section contains the information
- Note any contradictions between different sections
"""

    return f"""
You are a health insurance specialist that answers STRICTLY based on the provided health policy document.

CRITICAL REQUIREMENTS:
- Answer ONLY using information found in the provided health policy context
- DO NOT use general medical or insurance knowledge
- VERIFY that the question is DIRECTLY answered in the health policy - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated health policy content
- ONLY answer if there is a CLEAR, DIRECT match between the question and policy content
- Provide EXTREMELY CONCISE responses (1-2 lines maximum)
- Use plain language instead of medical/insurance jargon
- Extract exact figures (₹ amounts, percentages, waiting periods) directly from the document
- Always reference specific sections, pages, or tables where information is found
- If information is not in the context, clearly state "[Specific detail] is not provided in the health policy document"
- Cross-check multiple sections for complete coverage information
- Never add information not found in the context
- Keep answers brief and to the point
- DO NOT give advice or suggestions when information is missing - just state what's not provided

SMART VERIFICATION FOR HEALTH POLICY:
1. Does the health policy explicitly mention the exact medical condition/treatment being asked about?
2. Is the specific coverage/exclusion clearly stated (not implied)?
3. Can I quote the exact sentence that answers this question?
4. Am I making assumptions based on similar but different medical terms?
If ANY answer is NO, respond with "Information not provided in the health policy document"

{cross_check_instructions}

RESPONSE STYLE EXAMPLES:
- "According to the policy, arthritis has a 2-year waiting period but chronic arthritis is permanently excluded."
- "As per page 8, abortion is covered under maternity benefits with ₹25,000 limit, excluding elective procedures."
- "The policy specifies hydrocele surgery coverage after 1-year waiting period with ₹50,000 limit."
- "Specialist consultation fees are not provided in the health policy document."
- "Pre-authorization requirements are not specified in the document."

EXAMPLES OF SMART HEALTH POLICY VERIFICATION:
- Question: "Is diabetes covered?" → If policy only mentions "chronic diseases" without specifying diabetes → "Information not provided in the health policy document"
- Question: "What is the room rent limit?" → If policy only mentions "hospitalization coverage" without room rent details → "Information not provided in the health policy document"
- Question: "Are mental health treatments covered?" → If policy only mentions "psychiatric disorders" without mental health → "Information not provided in the health policy document"

CONTEXT FROM HEALTH POLICY DOCUMENT:
{context}

QUESTION: {question}

Answer strictly based on the health policy document provided above. Include specific page/section references. Keep your response to 1-2 lines maximum. Respond in JSON format:
{{ "answer": "..." }}
"""


def build_history_prompt(question, context_chunks):
    """Build prompt for history related questions."""

    # Check for irrelevant questions
    if detect_irrelevant_question(question, context_chunks):
        return f"""
You are a history educator. The user has asked an inappropriate question.

QUESTION: {question}

Respond with strong language about inappropriate behavior. Respond in JSON format:
{{ "answer": "Inappropriate questions violate educational principles and historical ethics. Please focus on legitimate historical topics." }}
"""

    context = "\n---\n".join([c["text"] for c in context_chunks])
    return f"""
You are a history educator that answers STRICTLY based on the provided historical documents.

CRITICAL REQUIREMENTS:
- Answer ONLY using information found in the provided historical context
- DO NOT use your general historical knowledge or external information
- VERIFY that the question is DIRECTLY answered in the historical document - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated historical content
- ONLY answer if there is a CLEAR, DIRECT match between the question and document content
- Provide EXTREMELY CONCISE responses (1-2 lines maximum)
- Use clear, everyday language to explain historical concepts
- Extract exact dates, figures, and events directly from the document
- Always reference specific parts of the document where information is found
- If information is not in the context, clearly state "[Specific detail] is not provided in the historical document"
- Never add information not found in the context
- Keep answers brief and to the point
- DO NOT give advice or suggestions when information is missing - just state what's not provided

SMART VERIFICATION FOR HISTORICAL DOCUMENTS:
1. Does the document explicitly mention the exact historical event/person/date being asked about?
2. Is the specific information clearly stated (not implied or suggested)?
3. Can I quote the exact sentence that answers this question?
4. Am I making assumptions based on similar but different historical information?
If ANY answer is NO, respond with "Information not provided in the historical document"

RESPONSE STYLE EXAMPLES:
- "According to the document, World War II lasted from 1939 to 1945 and resulted in 70-85 million fatalities."
- "As per the text, the Mughal Empire was founded by Babur in 1526 and peaked under Akbar (1556-1605)."
- "The document states the Industrial Revolution began in Britain around 1760, transforming manufacturing systems."
- "The exact battle casualties are not provided in the historical document."
- "Treaty signing dates are not specified in the document."

EXAMPLES OF SMART HISTORICAL VERIFICATION:
- Question: "When did Napoleon die?" → If document only mentions "Napoleon's exile" without death date → "Information not provided in the historical document"
- Question: "What caused World War I?" → If document only mentions "World War I occurred" without causes → "Information not provided in the historical document"
- Question: "Who was the first Mughal emperor?" → If document only mentions "Mughal rulers" without specifying first → "Information not provided in the historical document"

CONTEXT FROM HISTORICAL DOCUMENTS:
{context}

QUESTION: {question}

Answer strictly based on the historical documents provided above. Include specific references to the document. Keep your response to 1-2 lines maximum. Respond in JSON format:
{{ "answer": "..." }}
"""







# Adaptive Prompt Selection Function
def get_adaptive_prompt(question, context_chunks):
    """
    Select and build the appropriate prompt based on query classification.

    Args:
        question (str): The user's question
        context_chunks (list): Context chunks for the question

    Returns:
        tuple: (prompt, domain) - The appropriate prompt and domain for the classified query type
    """
    # Step 1: Classify the user query
    query_category = classify_user_query(question)

    # Step 2: Call the respective prompt function based on classification
    prompt_functions = {
        "general": build_general_prompt,
        "insurance": build_insurance_prompt,
        "health_policy": build_health_policy_prompt,
        "history": build_history_prompt
    }

    # Get the appropriate prompt function
    prompt_function = prompt_functions.get(query_category, build_general_prompt)

    # Build and return the prompt with domain
    prompt = prompt_function(question, context_chunks)
    return prompt, query_category


def build_insurance_prompt_with_routing(question, context_chunks, routing_info):
    """
    Builds a routing-aware prompt that adapts based on query type.

    Parameters:
        question (str): The user's question.
        context_chunks (list of dict): List of context dictionaries with a 'text' key.
        routing_info (dict): Query routing information.

    Returns:
        str: The formatted prompt to send to the LLM.
    """
    context = "\n---\n".join([c["text"] for c in context_chunks])
    primary_route = routing_info.get("primary_route", "general")
    processing_hints = routing_info.get("processing_hints", {})
    response_style = processing_hints.get("response_style", "general")

    # Customize instructions based on query type
    style_instructions = {
        "explanatory": "Provide a clear, detailed explanation of the concept or term.",
        "confirmatory": "Give a direct yes/no answer followed by brief supporting details.",
        "restrictive": "Clearly state what is NOT covered and explain any exceptions.",
        "instructional": "Provide step-by-step instructions or process details.",
        "specific": "Focus on exact numbers, amounts, and specific conditions.",
        "numerical": "Emphasize financial amounts, percentages, and calculations."
    }

    style_instruction = style_instructions.get(response_style, "Provide a helpful, direct answer.")

    return f"""
You are a helpful insurance assistant explaining policy details in simple terms. Answer questions about insurance policies in a friendly, conversational tone.

*QUERY TYPE:* {primary_route.upper()}
*RESPONSE STYLE:* {style_instruction}

*CRITICAL REQUIREMENTS:*
- Keep responses short and concise (1-2 lines whenever possible)
- Use plain, everyday language instead of technical insurance jargon
- Include only the most essential information like key numbers, conditions, and limits
- Be direct and get straight to the point
- Never add information not found in the context
- VERIFY that the question is DIRECTLY answered in the document - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated document content
- ONLY answer if there is a CLEAR, DIRECT match between the question and document content
- If information is missing, simply state "[Specific detail] is not provided in the document"
- DO NOT give advice or suggestions when information is missing

*SMART VERIFICATION:*
1. Is the exact question topic explicitly mentioned in the document?
2. Is the specific information clearly stated (not implied)?
3. Can I point to the exact sentence that answers this question?
4. Am I making any assumptions or inferences?
If ANY answer is NO, respond with "Information not provided in the document"

*CONTEXT FROM POLICY DOCUMENT:*
{context}

*QUESTION:* {question}

Provide a short, friendly answer using simple language. Respond in JSON format:
{{ "answer": "..." }}
"""


def call_gpt_fast(prompt, domain="general"):
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",  # Faster and more cost-effective
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,  # More deterministic for consistent outputs
            max_tokens=RESPONSE_CONFIG["max_tokens_per_response"],  # Use configured token limit
            top_p=0.1,  # More focused on highest probability tokens
            response_format={"type": "json_object"}  # Force JSON output format
        )
        content = response.choices[0].message.content

        try:
            # Parse the JSON response
            parsed_json = json.loads(content)
            answer = parsed_json.get("answer")
            if answer and answer.strip():
                # Validate and format the response
                formatted_answer = validate_and_format_response(answer, domain)
                return formatted_answer
        except json.JSONDecodeError:
            # Optimized fallback extraction
            if '{"answer":' in content:
                try:
                    start = content.find('{"answer":')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    raw_answer = json.loads(json_str).get("answer", "Information is not provided in the document.")
                    return validate_and_format_response(raw_answer, domain)
                except:
                    pass

            # Last resort: regex extraction
            answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', content)
            if answer_match:
                raw_answer = answer_match.group(1)
                return validate_and_format_response(raw_answer, domain)

        return "Information is not provided in the document."
    except Exception as e:
        return f"Error: {str(e)}"


def handle_zip_file(document_url, questions):
    """Handle ZIP file processing and questions."""
    try:
        # Process the ZIP file
        zip_result = process_zip_from_url(document_url)

        if not zip_result["success"]:
            return jsonify({
                "error": "ZIP processing failed",
                "details": zip_result.get("error", "Unknown error")
            }), 400

        # Generate answers based on ZIP content using parallel processing
        def process_zip_question(question):
            # For ZIP files, we provide general information about the archive
            if "how many" in question.lower() and "file" in question.lower():
                return f"The ZIP archive contains {zip_result['total_files']} files."
            elif "what" in question.lower() and ("type" in question.lower() or "kind" in question.lower()):
                file_types = zip_result.get('file_types', {})
                if file_types:
                    type_summary = ", ".join([f"{count} {ext} files" for ext, count in file_types.items()])
                    return f"The ZIP archive contains: {type_summary}."
                else:
                    return "The ZIP archive contains various file types."
            elif "nested" in question.lower() or "zip" in question.lower():
                nested_count = zip_result.get('total_nested_zips', 0)
                if nested_count > 0:
                    return f"The archive contains {nested_count} nested ZIP files."
                else:
                    return "The archive does not contain any nested ZIP files."
            elif "size" in question.lower():
                size_kb = zip_result.get('size_kb', 0)
                return f"The ZIP archive is {size_kb:.2f} KB in size."
            else:
                # For general questions, provide the summary
                return zip_result.get('summary', 'ZIP file processed successfully.')

        # Use enhanced parallel processing with classification
        answers = parallel_process_questions_simple(questions, process_zip_question)

        return jsonify({"answers": answers}), 200

    except Exception as e:
        return jsonify({
            "error": "ZIP processing error",
            "details": str(e)
        }), 500


def is_image_url(url):
    """Check if the URL points to an image file (.jpg, .jpeg, .png)."""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url.lower())
        path = parsed_url.path

        # Check file extension
        if path.endswith(('.jpg', '.jpeg', '.png')):
            return True

        # Check content type by making a HEAD request
        try:
            response = requests.head(url, timeout=10)
            content_type = response.headers.get('Content-Type', '').lower()
            if any(img_type in content_type for img_type in ['image/jpeg', 'image/jpg', 'image/png']):
                return True
        except:
            pass

        return False
    except:
        return False


def is_pptx_url(url):
    """Check if the URL points to a PowerPoint (.pptx) file."""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url.lower())
        path = parsed_url.path

        # Check file extension
        if path.endswith('.pptx') or path.endswith('.ppt'):
            return True

        # Check content type by making a HEAD request
        try:
            response = requests.head(url, timeout=10)
            content_type = response.headers.get('Content-Type', '').lower()
            if 'presentation' in content_type or 'powerpoint' in content_type:
                return True
        except:
            pass

        return False
    except:
        return False


def is_bin_url(url):
    """Check if the URL points to a binary (.bin) file."""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url.lower())
        path = parsed_url.path

        # Check file extension
        if path.endswith('.bin'):
            return True

        # Check content type by making a HEAD request
        try:
            response = requests.head(url, timeout=10)
            content_type = response.headers.get('Content-Type', '').lower()
            if 'application/octet-stream' in content_type or 'binary' in content_type:
                # Additional check for .bin in filename or URL
                if '.bin' in url.lower():
                    return True
        except:
            pass

        return False
    except:
        return False


def handle_image_file(document_url, questions):
    """Handle image files (.jpg, .jpeg, .png) using OpenAI Vision API."""
    try:
        # Validate image URL accessibility
        try:
            response = requests.head(document_url, timeout=10)
            response.raise_for_status()
        except Exception as e:
            return jsonify({
                "error": "Image URL not accessible",
                "details": str(e)
            }), 400

        def process_image_question(question):
            try:
                # Use OpenAI Vision API to analyze the image
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": f"Please analyze this image and answer the following question: {question}\n\nProvide a clear, concise answer (1-3 lines) based ONLY on what you can see in the image. Include specific details from the image in your response."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": document_url,
                                        "detail": "high"  # Use high detail for better analysis
                                    }
                                }
                            ]
                        }
                    ],
                    temperature=0.1,
                    max_tokens=500
                )

                answer = response.choices[0].message.content.strip()
                return answer

            except Exception as e:
                # Fallback answer for individual question failures
                return f"Unable to analyze image for this question: {str(e)}"

        # Use enhanced parallel processing with classification
        answers = parallel_process_questions_simple(questions, process_image_question)

        return jsonify({"answers": answers}), 200

    except Exception as e:
        return jsonify({
            "error": "Image processing error",
            "details": str(e)
        }), 500


def analyze_bin_file_url(url):
    """
    Analyze binary file URL to provide descriptive information.

    Args:
        url (str): The binary file URL

    Returns:
        str: Short description of the binary file
    """
    from urllib.parse import urlparse

    try:
        parsed_url = urlparse(url.lower())
        domain = parsed_url.netloc
        path = parsed_url.path
        filename = path.split('/')[-1] if path else ""

        # Common binary file patterns and descriptions
        descriptions = []

        # Check for common hosting providers and purposes
        if "hetzner" in domain:
            if "10gb.bin" in filename or "speed" in path:
                descriptions.append("This is a 10GB binary file hosted by Hetzner, a German cloud provider.")
                descriptions.append("It's commonly used for network speed and bandwidth testing on Hetzner's servers.")
            else:
                descriptions.append("This is a binary file hosted by Hetzner, a German cloud and dedicated server provider.")
                descriptions.append("It may be used for testing, configuration, or data storage purposes.")

        elif "speedtest" in domain or "speed" in path:
            descriptions.append("This appears to be a binary file used for internet speed testing.")
            descriptions.append("Such files help measure download speeds and network performance.")

        elif "firmware" in filename or "fw" in filename:
            descriptions.append("This appears to be a firmware binary file for device updates.")
            descriptions.append("It likely contains low-level software for hardware components.")

        elif "config" in filename or "cfg" in filename:
            descriptions.append("This appears to be a configuration binary file.")
            descriptions.append("It likely contains settings or parameters for software or hardware.")

        elif "backup" in filename or "bak" in filename:
            descriptions.append("This appears to be a backup binary file.")
            descriptions.append("It likely contains archived data or system backups.")

        elif "data" in filename or "db" in filename:
            descriptions.append("This appears to be a data binary file.")
            descriptions.append("It likely contains structured data or database information.")

        else:
            # Generic description based on file size indicators
            if any(size in filename for size in ["1gb", "5gb", "10gb", "100mb"]):
                descriptions.append("This is a binary test file used for network performance testing.")
                descriptions.append("Such files help measure download speeds and bandwidth capabilities.")
            else:
                descriptions.append(f"This is a binary file hosted at {domain}.")
                descriptions.append("Binary files contain non-text data and require specialized tools for analysis.")

        return " ".join(descriptions)

    except Exception:
        return "This is a binary file that contains non-text data. Binary files require specialized tools for proper analysis and interpretation."


def generate_direct_bin_answers(document_url, questions):
    """
    Generate direct answers for BIN file questions without LLM processing.

    Args:
        document_url (str): The binary file URL
        questions (list): List of questions

    Returns:
        list: List of answers
    """
    # Get the file description
    file_description = analyze_bin_file_url(document_url)

    def process_bin_question(question):
        question_lower = question.lower()

        # Customize response based on question type
        if any(word in question_lower for word in ['what is', 'describe', 'about', 'file']):
            return file_description
        elif any(word in question_lower for word in ['size', 'how big', 'large']):
            if any(size in document_url.lower() for size in ['10gb', '5gb', '1gb', '100mb']):
                size_match = next((size for size in ['10GB', '5GB', '1GB', '100MB'] if size.lower() in document_url.lower()), 'unknown size')
                return f"This binary file is {size_match} in size based on the URL. {file_description.split('.')[1].strip() if '.' in file_description else 'It is used for testing or data storage purposes.'}"
            else:
                return file_description
        elif any(word in question_lower for word in ['purpose', 'used for', 'why']):
            # Extract purpose from description
            if 'testing' in file_description.lower():
                return f"This file is primarily used for network speed and bandwidth testing. {file_description.split('.')[1].strip() if '.' in file_description else 'It helps measure download speeds and network performance.'}"
            elif 'firmware' in file_description.lower():
                return f"This file is used for firmware updates and device configuration. {file_description.split('.')[1].strip() if '.' in file_description else 'It contains low-level software for hardware components.'}"
            else:
                return file_description
        elif any(word in question_lower for word in ['provider', 'host', 'company']):
            # Extract provider information
            if 'hetzner' in file_description.lower():
                return "This file is hosted by Hetzner, a German cloud and dedicated server provider. Hetzner is known for providing high-performance servers and network infrastructure."
            else:
                return file_description
        else:
            # Default response
            return file_description

    # Use enhanced parallel processing with classification
    answers = parallel_process_questions_simple(questions, process_bin_question)

    return answers


def handle_bin_file(document_url, questions):
    """Handle binary (.bin) files with specialized analysis and description."""
    try:
        # First, try to generate direct answers without LLM
        direct_answers = generate_direct_bin_answers(document_url, questions)

        # Return direct answers immediately
        return jsonify({"answers": direct_answers}), 200

    except Exception as e:
        # Ultimate fallback
        file_description = analyze_bin_file_url(document_url)
        fallback_answers = [file_description for _ in questions]
        return jsonify({"answers": fallback_answers}), 200

    except Exception as e:
        return jsonify({
            "error": "Binary file processing error",
            "details": str(e)
        }), 500


def handle_pptx_file(document_url, questions):
    """Handle PowerPoint (.pptx) files with enhanced figure extraction and analysis."""
    try:
        # Create an enhanced prompt that includes specific instructions for figure extraction
        prompt = f"""
You are an AI assistant that analyzes PowerPoint presentations STRICTLY based on the content found in the slides.

PowerPoint File URL: {document_url}

CRITICAL ANALYSIS REQUIREMENTS:
- Answer ONLY using information found in the PowerPoint slides
- DO NOT use external knowledge or assumptions
- VERIFY that the question is DIRECTLY answered in the slides - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated slide content
- ONLY answer if there is a CLEAR, DIRECT match between the question and slide content
- Extract exact figures (e.g., ₹ amounts, waiting periods, percentages) directly from the slides
- Always reference specific slides where information is found (e.g., "As shown in slide X")
- If information is not in the presentation, clearly state "[Specific detail] is not provided in the presentation"
- Provide concise answers (1-2 lines maximum per question)
- DO NOT give advice or suggestions when information is missing

SMART VERIFICATION FOR SLIDES:
1. Does the slide explicitly mention the exact topic being asked about?
2. Is the specific information clearly stated (not implied)?
3. Can I quote the exact text from the slide that answers this question?
4. Am I making assumptions based on similar but different slide content?
If ANY answer is NO, respond with "Information not provided in the presentation"
- Note condition-based exclusions and limitations exactly as stated in slides
- Extract precise dates, timeframes, and eligibility criteria from the presentation

Questions to answer:
"""
        for i, question in enumerate(questions, 1):
            prompt += f"{i}. {question}\n"

        prompt += """
RESPONSE GUIDELINES:
- Answer strictly based on PowerPoint slide content only
- Include exact numerical values and slide references (e.g., "Slide 5 shows ₹50,000 limit")
- Mention specific exclusions and conditions exactly as presented in slides
- Provide concise answers (1-2 lines maximum per question)
- If tables or charts contain relevant data, extract the specific figures with slide references
- VERIFY that the question is DIRECTLY answered in the slides - do not infer or assume
- If the exact information requested is not explicitly stated, respond with "Information not provided"
- DO NOT get tricked by misleading or unrelated slide content
- If information is not available in slides, state "[Specific detail] is not provided in the presentation"
- DO NOT give advice or suggestions when information is missing

Access the PowerPoint file from the provided URL and analyze ONLY the slide content to answer each question.
Do not add any external information not found in the slides.

Respond in JSON format:
{ "answers": ["answer1", "answer2", ...] }
"""

        # Send to LLM for processing
        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1500,
                response_format={"type": "json_object"}
            )
            content = response.choices[0].message.content

            try:
                # Parse the JSON response
                parsed_json = json.loads(content)
                answers = parsed_json.get("answers", [])
                if answers and len(answers) == len(questions):
                    return jsonify({"answers": answers}), 200
                else:
                    # Fallback: create individual answers
                    fallback_answers = []
                    for question in questions:
                        fallback_answers.append("Please access the PowerPoint file directly to answer this question.")
                    return jsonify({"answers": fallback_answers}), 200
            except json.JSONDecodeError:
                # Fallback response
                fallback_answers = []
                for question in questions:
                    fallback_answers.append("Please access the PowerPoint file directly to answer this question.")
                return jsonify({"answers": fallback_answers}), 200

        except Exception as e:
            # Fallback response if LLM call fails
            fallback_answers = []
            for question in questions:
                fallback_answers.append("Please access the PowerPoint file directly to answer this question.")
            return jsonify({"answers": fallback_answers}), 200

    except Exception as e:
        return jsonify({
            "error": "PowerPoint processing error",
            "details": str(e)
        }), 500


def handle_excel_csv_file(document_url, questions):
    """Handle Excel/CSV file processing and questions."""
    try:
        # Process the Excel/CSV file
        data_result = process_excel_csv_from_url(document_url)

        if not data_result["success"]:
            return jsonify({
                "error": "Excel/CSV processing failed",
                "details": data_result.get("error", "Unknown error")
            }), 400

        # Generate answers based on the data using enhanced parallel processing with classification
        def process_data_question(question):
            return answer_question_from_data(question, data_result)

        # Use enhanced parallel processing with classification
        answers = parallel_process_questions_simple(questions, process_data_question)

        return jsonify({"answers": answers}), 200

    except Exception as e:
        return jsonify({
            "error": "Excel/CSV processing error",
            "details": str(e)
        }), 500


def handle_regular_document(document_url, questions):
    """Handle regular document processing (PDF, Word, etc.) with RAG."""
    try:
        cache_key = get_cache_key(document_url)
        cached = load_cache(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            text_by_page = extract_text_from_url(document_url)
            chunk_dicts = generate_smart_chunks(text_by_page)

            if not chunk_dicts:
                return jsonify({"error": "No valid content extracted from document"}), 400

            index, chunks, _ = embed_chunks_openai(chunk_dicts)
            save_cache(cache_key, {"index": index, "chunks": chunks})

        # Use enhanced parallel processing with classification and context retrieval
        answers = parallel_process_questions_with_context(questions, index, chunks)

        return jsonify({"answers": answers}), 200

    except Exception as e:
        return jsonify({
            "error": "Document processing error",
            "details": str(e)
        }), 500


@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
def run_submission():
    # Handle CORS preflight requests
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers

    try:
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            logger.warning(f"Unauthorized access attempt")
            return jsonify({"error": "Unauthorized"}), 401

        # Always use an extremely lenient JSON parser
        try:
            # First attempt: Use Flask's built-in parser with force=True
            try:
                data = request.get_json(force=True)
                if data:
                    pass
            except:
                # Second attempt: Try to parse raw data directly
                try:
                    # Try to decode with different encodings
                    try:
                        raw_data = request.data.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            raw_data = request.data.decode('latin-1')
                        except:
                            raw_data = request.data.decode('utf-8', errors='replace')

                    # Extract the URL and questions using regex (most reliable for malformed JSON)

                    # Look for URL in the document field (handles both quoted and unquoted)
                    # More comprehensive URL pattern that handles query params with special characters
                    url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
                    document_match = re.search(url_pattern, raw_data, re.IGNORECASE)

                    # Look for questions array
                    questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
                    questions_match = re.search(questions_pattern, raw_data, re.DOTALL)

                    if document_match and questions_match:
                        # Extract document URL - group(1) contains the full URL match
                        document_url = document_match.group(1).split('"')[0].split('\\')[0]
                        # Extract and clean up questions
                        questions_text = questions_match.group(1)
                        questions = []
                        # Extract individual questions with quotes (handles both single and double quotes)
                        for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                            if match.group(1):  # Double quotes match
                                questions.append(match.group(1))
                            else:  # Single quotes match
                                questions.append(match.group(2))

                        data = {"documents": document_url, "questions": questions}
                    else:
                        # If regex fails, try standard JSON parsing with cleaning
                        cleaned = raw_data.strip()
                        # Fix common JSON formatting issues
                        cleaned = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned)
                        data = json.loads(cleaned)

                except Exception as inner_e:
                    error_msg = f"All parsing attempts failed: {str(inner_e)}"
                    return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400

        except Exception as e:
            error_msg = f"JSON parsing error: {str(e)}"
            return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400

        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        # Security check for sensitive information requests
        security_check = check_security_sensitive_request(questions)
        if security_check["is_sensitive"]:
            return jsonify({
                "answers": [security_check["response"]] * len(questions)
            }), 200

        # Check document type and process accordingly
        try:
            # Check if it's an image file
            if is_image_url(document_url):
                return handle_image_file(document_url, questions)

            # Check if it's a PowerPoint file
            elif is_pptx_url(document_url):
                return handle_pptx_file(document_url, questions)

            # Check if it's a binary file
            elif is_bin_url(document_url):
                return handle_bin_file(document_url, questions)

            # Check if it's a ZIP file
            elif is_zip_url(document_url):
                return handle_zip_file(document_url, questions)

            # Check if it's an Excel or CSV file
            elif is_excel_or_csv_url(document_url):
                return handle_excel_csv_file(document_url, questions)

            # Default: handle as regular document (PDF, Word, etc.)
            else:
                return handle_regular_document(document_url, questions)

        except Exception as e:
            error_msg = f"Error determining document type: {str(e)}"
            return jsonify({"error": "Document processing error", "details": error_msg}), 500

    except Exception as e:
        error_msg = f"Server error: {str(e)}"
        return jsonify({"error": "Server error", "details": error_msg}), 500


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy"}), 200


@app.route("/debug/json", methods=["POST"])
def debug_json():
    """Debug endpoint to check JSON parsing"""
    try:
        # Raw data
        raw_data = request.data.decode('utf-8')

        # Try all parsing methods
        parsed = {}

        # Method 1: Standard JSON parsing
        try:
            parsed["standard"] = json.loads(raw_data)
        except Exception as e:
            parsed["standard_error"] = str(e)

        # Method 2: Flask's built-in parser
        try:
            parsed["flask"] = request.get_json(force=True)
        except Exception as e:
            parsed["flask_error"] = str(e)

        # Method 3: Cleaned regex approach
        try:
            cleaned_data = raw_data.strip()
            cleaned_data = re.sub(r'"\s*:\s*"', '":"', cleaned_data)
            cleaned_data = re.sub(r'"\s*,\s*"', '","', cleaned_data)
            cleaned_data = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned_data)
            parsed["cleaned"] = json.loads(cleaned_data)
        except Exception as e:
            parsed["cleaned_error"] = str(e)
            parsed["cleaned_data"] = cleaned_data

        # Return all results
        return jsonify({
            "raw_data": raw_data,
            "parsing_results": parsed,
            "content_type": request.content_type,
            "is_json": request.is_json
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# Request/Response logging middleware
@app.before_request
def log_request_info():
    """Log request information before processing"""
    request_id = hashlib.md5(f"{time.time()}{request.remote_addr}".encode()).hexdigest()[:8]
    request.request_id = request_id

    # Log basic request info
    request_logger.info(f"[{request_id}] REQUEST: {request.method} {request.url}")
    request_logger.info(f"[{request_id}] Remote Address: {request.remote_addr}")

    # Log request headers (excluding sensitive ones)
    headers = dict(request.headers)
    sensitive_headers = ['authorization', 'cookie', 'x-api-key']
    safe_headers = {k: v for k, v in headers.items() if k.lower() not in sensitive_headers}
    request_logger.info(f"[{request_id}] Request Headers: {safe_headers}")

    # Log request body for POST requests
    if request.method == 'POST':
        try:
            if request.is_json:
                body = request.get_json(force=True)
                request_logger.info(f"[{request_id}] Request Body (JSON): {json.dumps(body, indent=2)}")
            else:
                body = request.get_data(as_text=True)
                if len(body) > 1000:  # Truncate large bodies
                    body = body[:1000] + "... [TRUNCATED]"
                request_logger.info(f"[{request_id}] Request Body: {body}")
        except Exception as e:
            request_logger.error(f"[{request_id}] Error logging request body: {str(e)}")

@app.after_request
def log_response_info(response):
    """Log response information after processing"""
    request_id = getattr(request, 'request_id', 'unknown')

    # Log response status and headers
    request_logger.info(f"[{request_id}] RESPONSE: Status {response.status_code}")

    # Log response body for JSON responses
    if response.content_type == 'application/json':
        try:
            response_data = response.get_json()
            request_logger.info(f"[{request_id}] Response Body: {json.dumps(response_data, indent=2)}")
        except Exception as e:
            request_logger.error(f"[{request_id}] Error logging response body: {str(e)}")

    # Log processing time
    if hasattr(request, 'start_time'):
        processing_time = time.time() - request.start_time
        request_logger.info(f"[{request_id}] Processing Time: {processing_time:.3f}s")

    return response

@app.before_request
def start_timer():
    """Start timer for request processing"""
    request.start_time = time.time()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))