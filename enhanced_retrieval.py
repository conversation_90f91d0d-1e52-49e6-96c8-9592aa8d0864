"""
Enhanced Retrieval System with Hybrid Search (FAISS + BM25)
Combines semantic search with keyword-based search for better retrieval accuracy.
"""

import numpy as np
import faiss
from rank_bm25 import BM25<PERSON>kapi
from typing import List, Dict, Any, Tuple
import re
from openai import OpenAI
import os
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

class HybridRetriever:
    """
    Hybrid retrieval system combining FAISS (semantic) and BM25 (keyword-based) search.
    """
    
    def __init__(self, client: OpenAI, embedding_model: str = "text-embedding-3-large"):
        self.client = client
        self.embedding_model = embedding_model
        self.faiss_index = None
        self.bm25_index = None
        self.chunks = []
        self.chunk_embeddings = None
        self.tokenized_chunks = []
        
    def build_indices(self, chunks: List[Dict[str, Any]]) -> None:
        """
        Build both FAISS and BM25 indices from chunks.
        
        Args:
            chunks: List of chunk dictionaries with 'text' and other metadata
        """
        self.chunks = chunks
        texts = [chunk["text"] for chunk in chunks]
        
        # Build FAISS index
        logger.info("Building FAISS semantic index...")
        self._build_faiss_index(texts)
        
        # Build BM25 index
        logger.info("Building BM25 keyword index...")
        self._build_bm25_index(texts)
        
        logger.info(f"Hybrid indices built successfully for {len(chunks)} chunks")
    
    def _build_faiss_index(self, texts: List[str]) -> None:
        """Build FAISS index for semantic search."""
        try:
            # Process in batches to avoid API limits
            batch_size = 100
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                resp = self.client.embeddings.create(
                    model=self.embedding_model, 
                    input=batch_texts
                )
                batch_embeddings = [d.embedding for d in resp.data]
                all_embeddings.extend(batch_embeddings)
            
            # Convert to numpy array and normalize
            embeddings = np.array(all_embeddings, dtype=np.float32)
            self.chunk_embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]
            
            # Build FAISS index
            self.faiss_index = faiss.IndexFlatIP(self.chunk_embeddings.shape[1])
            self.faiss_index.add(self.chunk_embeddings)
            
        except Exception as e:
            logger.error(f"Error building FAISS index: {e}")
            raise
    
    def _build_bm25_index(self, texts: List[str]) -> None:
        """Build BM25 index for keyword search."""
        try:
            # Tokenize texts for BM25
            self.tokenized_chunks = [self._tokenize_text(text) for text in texts]
            
            # Build BM25 index
            self.bm25_index = BM25Okapi(self.tokenized_chunks)
            
        except Exception as e:
            logger.error(f"Error building BM25 index: {e}")
            raise
    
    @lru_cache(maxsize=1000)
    def _tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text for BM25 search with insurance-specific preprocessing.
        
        Args:
            text: Input text to tokenize
            
        Returns:
            List of tokens
        """
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep important punctuation
        text = re.sub(r'[^\w\s\-\$\%\.]', ' ', text)
        
        # Split into tokens
        tokens = text.split()
        
        # Filter out very short tokens and common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}
        
        filtered_tokens = [
            token for token in tokens 
            if len(token) > 2 and token not in stop_words
        ]
        
        return filtered_tokens
    
    def hybrid_search(
        self, 
        query: str, 
        k: int = 10, 
        semantic_weight: float = 0.7, 
        keyword_weight: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword-based retrieval.
        
        Args:
            query: Search query
            k: Number of results to return
            semantic_weight: Weight for semantic search scores
            keyword_weight: Weight for keyword search scores
            
        Returns:
            List of ranked chunks with combined scores
        """
        if not self.faiss_index or not self.bm25_index:
            raise ValueError("Indices not built. Call build_indices() first.")
        
        # Get semantic search results
        semantic_results = self._semantic_search(query, k * 2)  # Get more candidates
        
        # Get keyword search results
        keyword_results = self._keyword_search(query, k * 2)  # Get more candidates
        
        # Combine and rank results
        combined_results = self._combine_rankings(
            semantic_results, 
            keyword_results, 
            semantic_weight, 
            keyword_weight
        )
        
        # Return top k results
        return combined_results[:k]
    
    def _semantic_search(self, query: str, k: int) -> List[Tuple[int, float]]:
        """
        Perform semantic search using FAISS.
        
        Args:
            query: Search query
            k: Number of results
            
        Returns:
            List of (chunk_index, score) tuples
        """
        try:
            # Get query embedding
            q_resp = self.client.embeddings.create(
                model=self.embedding_model, 
                input=query
            )
            q_vec = np.array(q_resp.data[0].embedding, dtype=np.float32)
            q_vec = q_vec / np.linalg.norm(q_vec)
            
            # Search FAISS index
            scores, indices = self.faiss_index.search(np.array([q_vec]), k)
            
            # Return results as (index, score) tuples
            return [(int(idx), float(score)) for idx, score in zip(indices[0], scores[0])]
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []
    
    def _keyword_search(self, query: str, k: int) -> List[Tuple[int, float]]:
        """
        Perform keyword search using BM25.
        
        Args:
            query: Search query
            k: Number of results
            
        Returns:
            List of (chunk_index, score) tuples
        """
        try:
            # Tokenize query
            query_tokens = self._tokenize_text(query)
            
            # Get BM25 scores
            scores = self.bm25_index.get_scores(query_tokens)
            
            # Get top k indices
            top_indices = np.argsort(scores)[::-1][:k]
            
            # Return results as (index, score) tuples
            return [(int(idx), float(scores[idx])) for idx in top_indices]
            
        except Exception as e:
            logger.error(f"Error in keyword search: {e}")
            return []
    
    def _combine_rankings(
        self, 
        semantic_results: List[Tuple[int, float]], 
        keyword_results: List[Tuple[int, float]], 
        semantic_weight: float, 
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and keyword search results with weighted scoring.
        
        Args:
            semantic_results: Results from semantic search
            keyword_results: Results from keyword search
            semantic_weight: Weight for semantic scores
            keyword_weight: Weight for keyword scores
            
        Returns:
            List of ranked chunks with combined scores
        """
        # Normalize scores to [0, 1] range
        semantic_scores = self._normalize_scores([score for _, score in semantic_results])
        keyword_scores = self._normalize_scores([score for _, score in keyword_results])
        
        # Create score dictionaries
        semantic_dict = {idx: norm_score for (idx, _), norm_score in zip(semantic_results, semantic_scores)}
        keyword_dict = {idx: norm_score for (idx, _), norm_score in zip(keyword_results, keyword_scores)}
        
        # Combine scores
        combined_scores = {}
        all_indices = set(semantic_dict.keys()) | set(keyword_dict.keys())
        
        for idx in all_indices:
            sem_score = semantic_dict.get(idx, 0.0)
            key_score = keyword_dict.get(idx, 0.0)
            combined_score = (semantic_weight * sem_score) + (keyword_weight * key_score)
            combined_scores[idx] = combined_score
        
        # Sort by combined score
        sorted_indices = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Return enriched chunks
        results = []
        for idx, score in sorted_indices:
            chunk = self.chunks[idx].copy()
            chunk['combined_score'] = score
            chunk['semantic_score'] = semantic_dict.get(idx, 0.0)
            chunk['keyword_score'] = keyword_dict.get(idx, 0.0)
            results.append(chunk)
        
        return results
    
    def _normalize_scores(self, scores: List[float]) -> List[float]:
        """
        Normalize scores to [0, 1] range using min-max normalization.
        
        Args:
            scores: List of scores to normalize
            
        Returns:
            List of normalized scores
        """
        if not scores:
            return []
        
        min_score = min(scores)
        max_score = max(scores)
        
        if max_score == min_score:
            return [1.0] * len(scores)
        
        return [(score - min_score) / (max_score - min_score) for score in scores]
